import { gql } from 'graphql-request';

export const ARTICLE_LIST = gql`
    query Articles_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        articles_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                title
                sub_title
                brief_title
                slug
                desc
                content
                avatar1_id
                avatar2_id
                workflow_id
                article_type_id
                pseudonym_id
                web_layout_id
                mobile_layout_id
                department_id
                publish_date
                source
                file_id
                root_article_id
                lock_user_id
                lock_at
                press_publication_id
                issue_id
                typesetting_status_id
                is_unclassified
                workflow {
                    id
                    name
                    desc
                }
                pseudonym {
                    id
                    name
                }
                articleCategories {
                    id
                    article_id
                    category_id
                    display_order
                    category {
                        id
                        name
                        status_id
                        is_major
                        category_type_id
                    }
                }
                childrenArticles {
                    id
                    title
                    article_type_id
                    workflow_id
                    workflow {
                        id
                        name
                        desc
                    }
                    articleCategories {
                        id
                        article_id
                        category_id
                        display_order
                        category {
                            id
                            name
                            status_id
                            is_major
                            category_type_id
                        }
                    }
                    createdByUser {
                        id
                        full_name
                    }
                    updated<PERSON>y<PERSON>ser {
                        id
                        full_name
                    }
                    created_at
                    updated_at
                    updated_by
                    created_by
                }
                createdByUser {
                    id
                    full_name
                }
                updatedByUser {
                    id
                    full_name
                }
                created_at
                updated_at
                updated_by
                created_by
            }
        }
    }
`;

export const ARTICLES_DETAIL = gql`
    query Articles_detail($id: Int!) {
        articles_detail(id: $id) {
            id
            title
            sub_title
            brief_title
            slug
            desc
            content
            avatar1_id
            avatar2_id
            workflow_id
            article_type_id
            pseudonym_id
            web_layout_id
            mobile_layout_id
            department_id
            publish_date
            source
            file_id
            root_article_id
            lock_user_id
            lock_at
            press_publication_id
            issue_id
            typesetting_status_id
            is_unclassified
            avatar1 {
                file_url
            }
            avatar2 {
                file_url
            }
            workflow {
                id
                name
            }
            pseudonym {
                id
                name
            }
            articleKinds {
                article_id
                article_kind_id
            }
            articleTags {
                id
                article_id
                tag_id
                tag {
                    name
                }
            }
            articleCategories {
                id
                article_id
                category_id
                display_order
                is_major
                category {
                    id
                    name
                    status_id
                    is_major
                    category_type_id
                }
            }
            rootArticle {
                id
                title
            }
            relatedArticles {
                id
                article_id
                related_article_id
                article {
                    id
                    title
                }
                relatedArticle {
                    id
                    title
                }
            }
            lockUser {
                id
                full_name
            }
            articleFiles {
                id
                file_id
                file {
                    id
                    file_name
                    file_url
                    file_size
                    mime_type
                }
                is_royalty
                author {
                    id
                    full_name
                }
                createdByUser {
                    id
                    full_name
                }
            }
            articleIssuePages {
                id
                article_id
                issue_page_id
                display_order
                comment
            }
            articleRoyalties {
                id
                royalty_type_id
                suggest_royalty
                type_id
                articleRoyaltyUsers {
                    id
                    user_id
                    percent
                    comment
                }
            }
        }
    }
`;

export const ARTICLES_CREATE = gql`
    mutation Articles_create($body: ArticleSaveInputDto!) {
        articles_create(body: $body) {
            id
        }
    }
`;

export const ARTICLES_UPDATE = gql`
    mutation Articles_update($id: Int!, $body: ArticleSaveInputDto!) {
        articles_update(id: $id, body: $body) {
            id
        }
    }
`;

export const ARTICLES_DELETE = gql`
    mutation Articles_delete($id: Int!) {
        articles_delete(id: $id)
    }
`;

export const ARTICLE_NOTES_LIST = gql`
    query Article_notes_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        article_notes_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                created_by
                updated_by
                created_at
                updated_at
                article_id
                content
                createdByUser {
                    id
                    full_name
                }
            }
        }
    }
`;

export const ARTICLE_NOTES_CREATE = gql`
    mutation Article_notes_create($body: ArticleNoteSaveInputDto!) {
        article_notes_create(body: $body) {
            id
        }
    }
`;

export const ARTICLE_LOGS_LIST = gql`
    query Article_logs_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        article_logs_list(body: { page: $page, limit: $limit, filters: $filters, sorts: $sorts, search: $search }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                article_id
                content
                created_at
            }
        }
    }
`;

export const ARTICLES_CHANGE_LOCK = gql`
    mutation Articles_change_lock($id: Int!, $lock: Boolean!) {
        articles_change_lock(id: $id, lock: $lock)
    }
`;

export const ARTICLE_FILES_LIST = gql`
    query Article_files_list($page: Int!, $limit: Int!, $filters: [String!], $sorts: [String!], $search: String) {
        article_files_list(body: { search: $search, filters: $filters, sorts: $sorts, page: $page, limit: $limit }) {
            totalCount
            totalPages
            currentPage
            data {
                id
                article_id
                file_id
                file {
                    id
                    file_name
                    file_url
                    file_size
                    mime_type
                    folder_id
                    department_id
                    parent_id
                    is_newsroom
                    file_title
                    tags {
                        id
                        name
                    }
                }
                is_royalty
                author_id
                royalty_cost
            }
        }
    }
`;

export const ARTICLE_CHANGE_TYPE_SETTING_STATUS = gql`
    mutation Articles_change_typesetting_status($id: Int!, $typesetting_status_id: Int!) {
        articles_change_typesetting_status(id: $id, body: { typesetting_status_id: $typesetting_status_id })
    }
`;

export const ARTICLE_ISSUE_PAGE_CREATE_BATCH = gql`
    mutation Article_issue_pages_create_batch($article_ids: [Int!]!, $issue_page_id: Int!) {
        article_issue_pages_create_batch(body: { article_ids: $article_ids, issue_page_id: $issue_page_id }) {
            id
        }
    }
`;

export const ARTICLE_ISSUE_PAGE_UPDATE_PAGE = gql`
    mutation Article_issue_pages_update_page($id: Int!, $issue_page_id: Int!) {
        article_issue_pages_update_page(id: $id, body: { issue_page_id: $issue_page_id }) {
            id
        }
    }
`;

export const ARTICLES_CLONE = gql`
    mutation Articles_clone($id: Int!, $article_type_id: Int!) {
        articles_clone(id: $id, body: { article_type_id: $article_type_id }) {
            id
        }
    }
`;

export const ARTICLE_ISSUE_PAGE_DELETE = gql`
    mutation Article_issue_pages_delete($id: Int!) {
        article_issue_pages_delete(id: $id)
    }
`;

export const ARTICLE_ISSUE_PAGE_UPDATE_POSITION = gql`
    mutation Article_issue_pages_update_position($items: [ArticleIssuePagePositionItem!]!) {
        article_issue_pages_update_position(body: { items: $items }) {
            id
        }
    }
`;

export const ARTICLE_ISSUE_PAGE_CREATE = gql`
    mutation Article_issue_pages_create(
        $article_id: Int!
        $issue_page_id: Int
        $display_order: Int!
        $comment: String
        $position: JSON
    ) {
        article_issue_pages_create(
            body: {
                article_id: $article_id
                issue_page_id: $issue_page_id
                display_order: $display_order
                comment: $comment
                position: $position
            }
        ) {
            id
        }
    }
`;
