import { NotFound, PrivateRoute } from 'components/commons';
import React from 'react';
import { BrowserRouter, Route, Routes } from 'react-router-dom';

const AuthPage = React.lazy(() => import('features/auth/pages/AuthPage'));
const LoginPage = React.lazy(() => import('features/auth/pages/Login'));
const ForgotPage = React.lazy(() => import('features/auth/pages/ForgotPass'));
const ChangePassPage = React.lazy(() => import('features/auth/pages/ChangePass'));

const Dashboard = React.lazy(() => import('features/dashboard/pages/Dashboard'));
const UserList = React.lazy(() => import('features/user/pages/UserList'));
const UserAdd = React.lazy(() => import('features/user/pages/UserAdd'));
const UserEdit = React.lazy(() => import('features/user/pages/UserEdit'));
const UserProfile = React.lazy(() => import('features/user/pages/UserProfile'));
const UserPseudonym = React.lazy(() => import('features/user/pages/UserPseudonym'));
const LoginDeviceList = React.lazy(() => import('features/user/pages/LoginDeviceList'));
const NotificationList = React.lazy(() => import('features/user/pages/NotificationList'));
const ActionList = React.lazy(() => import('features/setting/pages/ActionList'));
const GroupActionList = React.lazy(() => import('features/setting/pages/GroupActionList'));
const GroupList = React.lazy(() => import('features/setting/pages/GroupList'));
const DepartmentList = React.lazy(() => import('features/setting/pages/DepartmentList'));

const WorkflowList = React.lazy(() => import('features/workflow/pages/WorkflowList'));
const WorkflowPermissionList = React.lazy(() => import('features/workflow/pages/WorkflowPermissionList'));
const TagList = React.lazy(() => import('features/taxonomy/pages/TagList'));
const CategoryList = React.lazy(() => import('features/taxonomy/pages/CategoryList'));
const LayoutList = React.lazy(() => import('features/layout/pages/LayoutList'));
const LayoutAdd = React.lazy(() => import('features/layout/pages/LayoutAdd'));
const LayoutEdit = React.lazy(() => import('features/layout/pages/LayoutEdit'));
const PortletList = React.lazy(() => import('features/portlet/pages/PortletList'));
const PortletAdd = React.lazy(() => import('features/portlet/pages/PortletAdd'));
const PortletEdit = React.lazy(() => import('features/portlet/pages/PortletEdit'));
const ConfigList = React.lazy(() => import('features/setting/pages/ConfigList'));
const AdvertiseList = React.lazy(() => import('features/advertise/pages/AdvertiseList'));
const AdvertiseItemList = React.lazy(() => import('../features/advertise/pages/AdvertiseItemList'));
const TemplateList = React.lazy(() => import('features/template/pages/TemplateList'));
const TemplateAdd = React.lazy(() => import('features/template/pages/TemplateAdd'));
const TemplateEdit = React.lazy(() => import('features/template/pages/TemplateEdit'));
const ArticleAdd = React.lazy(() => import('../features/article/pages/ArticleAdd'));
const ArticleEdit = React.lazy(() => import('../features/article/pages/ArticleEdit'));
const ArticlePage = React.lazy(() => import('features/article/pages/ArticlePage'));
const ArticleRoyaltiesPage = React.lazy(() => import('features/article/pages/ArticleRoyaltiesPage'));
const ArticleRoyaltyTypeList = React.lazy(() => import('features/article/pages/ArticleRoyaltyTypeList'));
const FolderFile = React.lazy(() => import('features/folderFile/pages/FolderFile'));
const PaperPagePlan = React.lazy(() => import('features/article/pages/PaperPagePlan'));
const PaperPagePlanEditor = React.lazy(() => import('features/article/pages/PaperPagePlanEditor'));
const PdfViewerPage = React.lazy(() => import('features/pdfViewer/pages/PdfViewerPage'));
const ListArticleCategory = React.lazy(() => import('features/taxonomy/pages/ListArticleCategory'));
const PressPublicationList = React.lazy(() => import('features/issue/pages/PressPublicationList'));
const IssueList = React.lazy(() => import('features/issue/pages/IssueList'));
const IssuePageList = React.lazy(() => import('features/issue/pages/IssuePageList'));
const IssuePageFileList = React.lazy(() => import('features/issue/pages/IssuePageFileList'));

export default function RouterView() {
    return (
        <BrowserRouter>
            <Routes>
                <Route path="/" element={<AuthPage />}>
                    <Route index element={<LoginPage />} />
                    <Route path="forgot" element={<ForgotPage />} />
                    <Route path="change-password" element={<ChangePassPage />} />
                </Route>
                <Route
                    path="/dashboard"
                    element={
                        <PrivateRoute>
                            <Dashboard />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/list/:type"
                    element={
                        <PrivateRoute>
                            <UserList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/add/:type"
                    element={
                        <PrivateRoute>
                            <UserAdd />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/edit/:id"
                    element={
                        <PrivateRoute>
                            <UserEdit />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/profile"
                    element={
                        <PrivateRoute>
                            <UserProfile />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/user/pseudonym/:userId"
                    element={
                        <PrivateRoute>
                            <UserPseudonym />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/loginDevice"
                    element={
                        <PrivateRoute>
                            <LoginDeviceList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/notification"
                    element={
                        <PrivateRoute>
                            <NotificationList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/action"
                    element={
                        <PrivateRoute>
                            <ActionList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/group/role"
                    element={
                        <PrivateRoute>
                            <GroupActionList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/group/:type"
                    element={
                        <PrivateRoute>
                            <GroupList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/department"
                    element={
                        <PrivateRoute>
                            <DepartmentList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/workflow"
                    element={
                        <PrivateRoute>
                            <WorkflowList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/workflowPermission"
                    element={
                        <PrivateRoute>
                            <WorkflowPermissionList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/tag"
                    element={
                        <PrivateRoute>
                            <TagList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/taxonomy/:type1/:type2"
                    element={
                        <PrivateRoute>
                            <CategoryList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/layout"
                    element={
                        <PrivateRoute>
                            <LayoutList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/layout/add"
                    element={
                        <PrivateRoute>
                            <LayoutAdd />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/layout/edit/:id"
                    element={
                        <PrivateRoute>
                            <LayoutEdit />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/template"
                    element={
                        <PrivateRoute>
                            <TemplateList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/template/add"
                    element={
                        <PrivateRoute>
                            <TemplateAdd />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/template/edit/:id"
                    element={
                        <PrivateRoute>
                            <TemplateEdit />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/portlet"
                    element={
                        <PrivateRoute>
                            <PortletList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/portlet/add"
                    element={
                        <PrivateRoute>
                            <PortletAdd />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/portlet/edit/:id"
                    element={
                        <PrivateRoute>
                            <PortletEdit />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/article/:type"
                    element={
                        <PrivateRoute>
                            <ArticlePage />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/article/add/:type/:articleTypeId"
                    element={
                        <PrivateRoute>
                            <ArticleAdd />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/article/edit/:type/:articleTypeId/:id"
                    element={
                        <PrivateRoute>
                            <ArticleEdit />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/articleRoyalties"
                    element={
                        <PrivateRoute>
                            <ArticleRoyaltiesPage />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/config"
                    element={
                        <PrivateRoute>
                            <ConfigList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/advertise"
                    element={
                        <PrivateRoute>
                            <AdvertiseList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/advertiseItem"
                    element={
                        <PrivateRoute>
                            <AdvertiseItemList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/folder"
                    element={
                        <PrivateRoute>
                            <FolderFile />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/pageLayout"
                    element={
                        <PrivateRoute>
                            <PaperPagePlan />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/pageLayout/edit/:id"
                    element={
                        <PrivateRoute>
                            <PaperPagePlanEditor />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/issueApprove/pdf-viewer/:id"
                    element={
                        <PrivateRoute>
                            <PdfViewerPage />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/taxonomy/:type1/:type2/:id"
                    element={
                        <PrivateRoute>
                            <ListArticleCategory />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/pressPublication"
                    element={
                        <PrivateRoute>
                            <PressPublicationList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/issue"
                    element={
                        <PrivateRoute>
                            <IssueList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/issuePage"
                    element={
                        <PrivateRoute>
                            <IssuePageList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/issueApprove"
                    element={
                        <PrivateRoute>
                            <IssuePageFileList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route
                    path="/articleRoyaltyType"
                    element={
                        <PrivateRoute>
                            <ArticleRoyaltyTypeList />
                        </PrivateRoute>
                    }
                ></Route>
                <Route path="/not-found" element={<NotFound />}></Route>
                <Route path="*" element={<NotFound />}></Route>
            </Routes>
        </BrowserRouter>
    );
}
