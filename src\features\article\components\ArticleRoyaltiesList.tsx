import { Table } from 'reactstrap';
import Spinner from '../../../components/partials/Spinner';
import { ArticleType } from '../../../types/common/Item';
import ArticleRoyaltiesElectronicArticleList from './ArticleRoyaltiesElectronicArticleList';
import ArticleRoyaltiesAllArticleList from './ArticleRoyaltiesAllArticleList';
import Article, {
    ArticleQueryRes,
    NewArticleType,
    SearchArticleParam,
} from '../../../types/Article';
import { convertPaging } from '../../../utils/common';

interface IProps {
    activeTab: string;
    isLoading: boolean;
    data: ArticleQueryRes;
    limit: number;
    articleTypeId: number;
}

export default function ArticleRoyaltiesList({
    activeTab,
    isLoading,
    data,
    limit,
    articleTypeId,
}: Readonly<IProps>) {
    const articles = data?.articles_list?.data || [];
    const paging = convertPaging<Article, SearchArticleParam>(data.articles_list, limit);

    const renderTableBody = () => {
        if (articles.length === 0) {
            return (
                <tbody>
                    <tr>
                        <td colSpan={6} className="text-center">Không có dữ liệu</td>
                    </tr>
                </tbody>
            );
        }

        if (activeTab === NewArticleType.ALL) {
            return <ArticleRoyaltiesAllArticleList articles={articles} paging={paging} />;
        }

        if (
            activeTab === ArticleType.ELECTRONIC.toString() ||
            activeTab === ArticleType.PAPER.toString() ||
            activeTab === ArticleType.TELEVISION.toString() ||
            activeTab === ArticleType.VIDEO.toString()
        ) {
            return (
                <ArticleRoyaltiesElectronicArticleList articles={articles} paging={paging} />
            );
        }

        // Fallback cho các tab khác
        return (
            <tbody>
                <tr>
                    <td colSpan={6} className="text-center">Không có dữ liệu</td>
                </tr>
            </tbody>
        );
    };

    const renderTableUI = () => (
        <Table responsive>
            <thead>
                <tr>
                    <th>STT</th>
                    <th>Tiêu đề</th>
                    <th>Thể loại tin</th>
                    <th>Chuyên mục</th>
                    <th>Người biên tập</th>
                    <th className="text-center">Trạng thái</th>
                    <th className="text-center">Xử lý</th>
                </tr>
            </thead>
            {renderTableBody()}
        </Table>
    );

    return (
        <div className="card">
            <div className="card-body">
                {isLoading ? <Spinner /> : renderTableUI()}
            </div>
        </div>
    );
}
