import { ARTICLE_TAB } from 'constants/common';
import { useState } from 'react';
import { Eye, X } from 'react-feather';
import User from 'types/User';
import ArticleLog, { ArticleLogAction } from '../../../types/ArticleLog';
import { FORMAT_DATE, formatDateTime } from '../../../utils/date';
import ModalViewArticleLog from './ModalViewArticleLog';

interface UpdateArticleFormTabHistoryProps {
    setTabAction(tab: ARTICLE_TAB | null): void;
    articleLogs: ArticleLog[];
    users: User[];
}

export const UpdateArticleFormTabHistory = ({ setTabAction, articleLogs, users }: UpdateArticleFormTabHistoryProps) => {
    const [isShowLogDetail, setIsShowLogDetail] = useState(false);
    const [selectedArticleLog, setSelectedArticleLog] = useState<ArticleLog | undefined>();

    const parseContent = (log: ArticleLog) => JSON.parse(log.content);

    const getUser = (log: ArticleLog) => {
        const userId = parseContent(log).user_id;

        return users.find((u) => u.id === userId)?.full_name;
    };

    const getActionText = (log: ArticleLog) => {
        const action = parseContent(log).action;

        switch (action) {
            case ArticleLogAction.ARTICLE_UPDATED:
                return 'Chỉnh sửa bài viết';

            case ArticleLogAction.ARTICLE_CREATED:
                return 'Tạo mới bài viết';

            default:
                return '';
        }
    };

    const onShowLogDetail = (log: ArticleLog) => {
        setIsShowLogDetail(true);
        setSelectedArticleLog(log);
    };

    const onCloseLogDetail = (isOpen: boolean) => {
        setIsShowLogDetail(isOpen);
        setSelectedArticleLog(undefined);
    };

    return (
        <>
            <div className="card !mb-0">
                <div className="card-header bg-[#FCF3F5FF] p-[.75rem] py-25">
                    <h4 className="card-title !text-base text-[#A42D49FF]">Lịch sử thay đổi</h4>
                    <div className="heading-elements">
                        <ul className="mb-0 list-inline">
                            <li>
                                <X
                                    size={24}
                                    className="text-[#A42D49FF] pt-1.5 cursor-pointer"
                                    onClick={() => setTabAction(null)}
                                />
                            </li>
                        </ul>
                    </div>
                </div>
                <div>
                    <div className="card-body p-[.75rem]">
                        <div className="mt-1 mb-1">
                            <div className="table-responsive">
                                <table
                                    className="table"
                                    style={{
                                        tableLayout: 'fixed',
                                    }}
                                >
                                    <thead>
                                        <tr>
                                            <th className="text-center !px-2" style={{ width: '128px' }}>
                                                Người thực hiện
                                            </th>
                                            <th className="text-center !px-2" style={{ width: '128px' }}>
                                                Loại
                                            </th>
                                            <th className="text-center !px-2" style={{ width: '132px' }}>
                                                Thời gian
                                            </th>
                                            <th className="text-center !px-2" style={{ width: '48px' }}>
                                                Xem
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {articleLogs.map((item) => (
                                            <tr key={item.id}>
                                                <td className="text-center !px-2 truncate">{getUser(item)}</td>
                                                <td className="text-center !px-2 truncate">{getActionText(item)}</td>
                                                <td className="text-center !px-2 truncate">
                                                    {formatDateTime(
                                                        item.created_at?.toString() ?? '',
                                                        FORMAT_DATE.SHOW_DATE_MINUTE
                                                    )}
                                                </td>
                                                <td className="text-center !px-2">
                                                    {parseContent(item).action === ArticleLogAction.ARTICLE_UPDATED && (
                                                        <button
                                                            type="button"
                                                            title="Xem"
                                                            className="btn btn-icon btn-sm btn-flat-danger waves-effect"
                                                            onClick={() => onShowLogDetail(item)}
                                                        >
                                                            <Eye size={14} />
                                                        </button>
                                                    )}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <ModalViewArticleLog articleLog={selectedArticleLog} show={isShowLogDetail} changeShow={onCloseLogDetail} />
        </>
    );
};
