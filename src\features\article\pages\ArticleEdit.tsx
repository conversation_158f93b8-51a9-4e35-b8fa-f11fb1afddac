import { Helmet } from 'react-helmet-async';
import { Navigate, useParams } from 'react-router-dom';
import { OPERATION_NAME, PATH, QUERY_KEY } from '../../../constants/common';
import { useGraphQLQuery } from '../../../hooks/useGraphQLQuery';
import { ARTICLES_DETAIL } from '../../../services/ArticleService';
import { ArticleDetailQueryRes, ArticlePageType } from '../../../types/Article';
import ArticleFormContainer from '../components/ArticleFormContainer';

export default function ArticleEdit() {
    const { type, articleTypeId, id } = useParams<{ type: ArticlePageType; articleTypeId: string; id: string }>();
    if (!type || !articleTypeId || !id) return null;

    const {
        data: articleData,
        isLoading,
        refetch: refetchArticle,
    } = useGraphQLQuery<ArticleDetailQueryRes, { id: number }>(
        [QUERY_KEY.ARTICLE, id],
        ARTICLES_DETAIL,
        { id: Number(id) },
        OPERATION_NAME.CALL_STATIC_TOKEN,
        {
            enabled: !!id,
        }
    );
    const article = articleData?.articles_detail;

    if (!isLoading && !article) return <Navigate to={PATH.NOT_FOUND} />;

    return (
        <>
            <Helmet>
                <title>Chỉnh sửa bài viết</title>
            </Helmet>
            <div className="content-body">
                <ArticleFormContainer
                    typeId={Number(articleTypeId)}
                    id={Number(id)}
                    type={type}
                    article={article}
                    refetchArticle={refetchArticle}
                />
            </div>
        </>
    );
}
