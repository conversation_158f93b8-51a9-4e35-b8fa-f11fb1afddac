import classNames from 'classnames';
import { ARTICLE_TAB } from 'constants/common';
import { Dispatch, SetStateAction, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';
import { Info, Minus, Plus, UploadCloud, X } from 'react-feather';
import { FieldErrors, UseFormRegister, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import Select, { SingleValue } from 'react-select';
import Article, { ArticlePageType } from 'types/Article';
import { ArticleType, ArticleTypeNames, ItemParamModel, SelectOptionModel, TypeMedia } from 'types/common/Item';
import Pseudonym from 'types/Pseudonym';
import { convertObjectToSelectOptions } from 'utils/common';
import Category from '../../../types/Category';
import CategoryListCheckbox, { CategoryItem } from './CategoryListCheckbox';
import { TFunction } from 'i18next';
import { Link } from 'react-router-dom';

interface UpdateArticleFormTabInformationProps {
    setTabAction(tab: ARTICLE_TAB | null): void;
    register: UseFormRegister<Article>;
    errors: FieldErrors<Article>;
    watch: UseFormWatch<Article>;
    setValue: UseFormSetValue<Article>;
    pseudonyms: Pseudonym[];
    onChangePseudonym(option: SingleValue<SelectOptionModel>): void;
    pseudonymValue: SelectOptionModel;
    topics: Category[];
    categories: Category[];
    onChangeTopic(option: SingleValue<SelectOptionModel>): void;
    topicValue: SelectOptionModel;
    setShowModalMedia(s: boolean): void;
    setTypeModalMedia(type: TypeMedia): void;
    avatars: {
        avatar1: string;
        avatar2: string;
    };
    mainCategoryIds: CategoryItem[];
    setMainCategoryIds(ids: CategoryItem[]): void;
    subCategoryIds: CategoryItem[];
    setSubCategoryIds(ids: CategoryItem[]): void;
    currentTypeId: number;
    t: TFunction<'translation', undefined>;
    id: number | null;
    rootArticle: Article | null;
    articlePageType: ArticlePageType;
    articleTypeId: ArticleType;
    setAvatars: Dispatch<
        SetStateAction<{
            avatar1: string;
            avatar2: string;
        }>
    >;
}

export const UpdateArticleFormTabInformation = ({
    setTabAction,
    register,
    errors,
    watch,
    setValue,
    pseudonyms,
    onChangePseudonym,
    pseudonymValue,
    topics,
    categories,
    onChangeTopic,
    topicValue,
    setShowModalMedia,
    setTypeModalMedia,
    avatars,
    mainCategoryIds,
    setMainCategoryIds,
    subCategoryIds,
    setSubCategoryIds,
    currentTypeId,
    t,
    id,
    rootArticle,
    articlePageType,
    articleTypeId,
    setAvatars,
}: UpdateArticleFormTabInformationProps) => {
    const [tab, setTab] = useState(1);
    const [openTitle, setOpenTitle] = useState(false);
    const [openImage, setOpenImage] = useState(false);

    const isShowSyncData = useMemo(
        () => articleTypeId === ArticleType.ELECTRONIC || articleTypeId === ArticleType.PAPER,
        [articleTypeId]
    );

    useEffect(() => {
        if ((!pseudonymValue || !pseudonymValue.value) && pseudonyms.length > 0) {
            const defaultItem = pseudonyms.find((item) => item.is_default) ?? pseudonyms[0];

            if (defaultItem) {
                const option = {
                    value: defaultItem.id?.toString() ?? '',
                    label: defaultItem.name,
                };
                onChangePseudonym(option);
            }
        }
    }, [onChangePseudonym, pseudonymValue, pseudonyms]);

    return (
        <div className="card !mb-0">
            <div className="card-header bg-[#FCF3F5FF] p-[.75rem] py-25">
                <h4 className="card-title !text-base text-[#A42D49FF]">Thông tin thiết yếu</h4>
                <div className="heading-elements">
                    <ul className="mb-0 list-inline">
                        <li>
                            <X
                                size={24}
                                className="text-[#A42D49FF] pt-1.5 cursor-pointer"
                                onClick={() => setTabAction(null)}
                            />
                        </li>
                    </ul>
                </div>
            </div>
            <div>
                <div className="card-body p-[.75rem]">
                    <div className="mt-1 mb-1">
                        <label className="form-label">
                            Tiêu đề <span className="error">*</span>
                        </label>
                        <input
                            {...register('title')}
                            className={classNames('form-control', {
                                error: Boolean(errors.title?.message),
                            })}
                            type="text"
                        />
                        <span className="error">{errors.title?.message}</span>
                    </div>

                    <div
                        className="mb-1 form-control flex justify-between items-center cursor-pointer
                    "
                        onClick={() => setOpenTitle((pre) => !pre)}
                    >
                        <label className="form-label mb-0 cursor-pointer">
                            {openTitle ? 'Ẩn tiêu đề phụ & tiêu đề rút gọn' : 'Thêm tiêu đề phụ & tiêu đề rút gọn'}
                        </label>
                        {openTitle ? <Minus size={14} /> : <Plus size={14} />}
                    </div>
                    {openTitle && (
                        <>
                            <div className="mb-1">
                                <label className="form-label">Tiêu đề phụ</label>
                                <input className="form-control" type="text" {...register('sub_title')} />
                            </div>
                            <div className="mb-1">
                                <label className="form-label">Tiêu đề rút gọn</label>
                                <input className="form-control" type="text" {...register('brief_title')} />
                            </div>
                        </>
                    )}
                    <div className="mb-1">
                        <label className="form-label">Trích dẫn</label>
                        <textarea className="form-control" rows={2} {...register('desc')} />
                    </div>

                    <div className="mb-1">
                        <label className="form-label">Ảnh đại diện</label>
                        <div className="w-full mx-auto">
                            <div className={classNames(`relative border-2 border-dashed rounded-lg p-6`)}>
                                <div className="text-center">
                                    <UploadCloud className="mx-auto h-12 w-12 text-gray-400" />
                                    <div className="mt-4">
                                        <button
                                            type="button"
                                            className="text-primary"
                                            onClick={() => {
                                                setTypeModalMedia('avatar1'), setShowModalMedia(true);
                                            }}
                                        >
                                            nhấn chọn file
                                        </button>
                                    </div>
                                    <p className="mt-2 text-sm text-gray-500">
                                        Hỗ trợ các định dạng: PNG, JPG, JPEG, GIF
                                    </p>
                                </div>

                                {watch('avatar1_id') && (
                                    <div className="relative mt-4">
                                        <img src={avatars.avatar1} alt="Preview" className="w-full h-auto rounded" />

                                        <div className="absolute top-2 right-2">
                                            <button
                                                type="button"
                                                onClick={() => {
                                                    setValue('avatar1_id', null);
                                                    setAvatars({ ...avatars, avatar1: '' });
                                                }}
                                                className="bg-white rounded-full p-2 shadow-lg"
                                            >
                                                <X className="h-5 w-5" />
                                            </button>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                    <div
                        className="mb-1 form-control flex justify-between items-center cursor-pointer
                    "
                        onClick={() => setOpenImage((pre) => !pre)}
                    >
                        <label className="form-label mb-0 cursor-pointer">
                            {openImage ? 'Ẩn ảnh đại diện khác' : 'Thêm ảnh đại diện khác'}
                        </label>
                        {openImage ? <Minus size={14} /> : <Plus size={14} />}
                    </div>
                    {openImage && (
                        <div className="mb-1">
                            <label className="form-label">Ảnh đại diện khác</label>
                            <div className="w-full mx-auto">
                                <div className={classNames(`relative border-2 border-dashed rounded-lg p-6`)}>
                                    <div className="text-center">
                                        <UploadCloud className="mx-auto h-12 w-12 text-gray-400" />
                                        <div className="mt-4">
                                            <button
                                                type="button"
                                                className="text-primary"
                                                onClick={() => {
                                                    setTypeModalMedia('avatar2'), setShowModalMedia(true);
                                                }}
                                            >
                                                nhấn chọn file
                                            </button>
                                        </div>
                                        <p className="mt-2 text-sm text-gray-500">
                                            Hỗ trợ các định dạng: PNG, JPG, JPEG, GIF
                                        </p>
                                    </div>

                                    {watch('avatar2_id') && (
                                        <div className="relative mt-4">
                                            <img
                                                src={avatars.avatar2}
                                                alt="Preview"
                                                className="w-full h-auto rounded"
                                            />

                                            <div className="absolute top-2 right-2">
                                                <button
                                                    type="button"
                                                    onClick={() => {
                                                        setValue('avatar2_id', null);
                                                        setAvatars({ ...avatars, avatar2: '' });
                                                    }}
                                                    className="bg-white rounded-full p-2 shadow-lg"
                                                >
                                                    <X className="h-5 w-5" />
                                                </button>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    )}
                    <div className="mb-1">
                        <label className="form-label">Tác giả</label>
                        <Select
                            options={convertObjectToSelectOptions(
                                pseudonyms.map((item) => ({
                                    id: item.id?.toString(),
                                    name: item.name,
                                })) as ItemParamModel[]
                            )}
                            onChange={onChangePseudonym}
                            value={pseudonymValue}
                            isClearable
                        />
                    </div>
                    <div className="mb-1">
                        <label className="form-label">Bút danh</label>
                        <input {...register('pseudonym_name')} className="form-control" type="text" />
                    </div>
                    <div className="mb-1">
                        <label className="form-label">
                            Chuyên mục
                            {/* <span className="error">*</span> */}
                        </label>
                        <ul className="nav nav-tabs">
                            <li className="nav-item">
                                <a
                                    className={classNames('nav-link text-sm', {
                                        active: tab === 1,
                                    })}
                                    onClick={() => setTab(1)}
                                >
                                    Chính
                                </a>
                            </li>
                            <li className="nav-item">
                                <a
                                    className={classNames('nav-link text-sm', {
                                        active: tab === 2,
                                    })}
                                    onClick={() => setTab(2)}
                                >
                                    Phụ
                                </a>
                            </li>
                            {articleTypeId === ArticleType.ELECTRONIC && (
                                <li className="nav-item">
                                    <a
                                        className={classNames('nav-link text-sm', {
                                            active: tab === 3,
                                        })}
                                        onClick={() => setTab(3)}
                                    >
                                        Sự kiện
                                    </a>
                                </li>
                            )}
                        </ul>
                        <div className="tab-content">
                            <div className={classNames('tab-pane', { active: tab === 1 })}>
                                <div className="table-responsive">
                                    <table className="table">
                                        <thead>
                                            <tr>
                                                <th>Chuyên mục</th>
                                                <th className="text-center w-1/6">Chuyên mục chính</th>
                                                <th className="text-center w-1/5">Thứ tự</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <CategoryListCheckbox
                                                categories={categories}
                                                isMajor={true}
                                                onChange={(selectedCategories) => {
                                                    setMainCategoryIds(selectedCategories);
                                                }}
                                                defaultSelectedCategories={mainCategoryIds}
                                            />
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div className={classNames('tab-pane', { active: tab === 2 })}>
                                <div className="table-responsive">
                                    <table className="table">
                                        <thead>
                                            <tr>
                                                <th>Chuyên mục</th>
                                                <th className="text-center w-1/6">Chuyên mục chính</th>
                                                <th className="text-center w-1/5">Thứ tự</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <CategoryListCheckbox
                                                categories={categories}
                                                isMajor={false}
                                                onChange={(selectedCategories) => {
                                                    setSubCategoryIds(selectedCategories);
                                                }}
                                                defaultSelectedCategories={subCategoryIds}
                                            />
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {articleTypeId === ArticleType.ELECTRONIC && (
                                <div className={classNames('tab-pane', { active: tab === 3 })}>
                                    <div className="mb-1">
                                        <Select
                                            options={convertObjectToSelectOptions(
                                                topics.map((item) => ({
                                                    id: item.id?.toString(),
                                                    name: item.name,
                                                })) as ItemParamModel[]
                                            )}
                                            onChange={onChangeTopic}
                                            value={topicValue}
                                            isClearable
                                            menuPlacement="auto"
                                            menuPosition="fixed"
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="mb-1">
                        <div className="d-flex flex-wrap">
                            <div className="form-check form-check-inline">
                                <input
                                    type="checkbox"
                                    className="form-check-input"
                                    id="is_unclassified"
                                    {...register('is_unclassified')}
                                />
                                <label className="form-check-label" htmlFor="is_unclassified">
                                    Chưa phân loại
                                </label>
                            </div>
                        </div>
                    </div>
                    {!id && isShowSyncData && (
                        <>
                            <div className="mb-1">
                                <label className="form-label">Sử dụng bài viết cho:</label>
                                <div className="d-flex flex-wrap">
                                    {ArticleTypeNames.filter(
                                        (item) =>
                                            item.id !== currentTypeId &&
                                            item.id !== ArticleType.VIDEO
                                    ).map((item) => (
                                        <div key={item.id} className="form-check form-check-inline">
                                            <input
                                                type="checkbox"
                                                className="form-check-input"
                                                id={`article-type-${item.id}`}
                                                value={item.id}
                                                {...register('article_type_copy_ids')}
                                                // checked={}
                                                // onChange={(e) => {}}
                                            />
                                            <label className="form-check-label" htmlFor={`article-type-${item.id}`}>
                                                {t(`${item.name}.single`)}
                                            </label>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            <div className="mb-1">
                                <label htmlFor="" className="form-label">
                                    Đồng bộ thông tin bài viết <Info size={14} className="cursor-pointer" />
                                </label>
                                <div className="d-flex flex-wrap">
                                    <div className="form-check form-check-inline">
                                        <input
                                            type="radio"
                                            className="form-check-input"
                                            id="sync"
                                            value={1}
                                            {...register('is_sync')}
                                        />
                                        <label className="form-check-label" htmlFor="sync">
                                            Đồng bộ
                                        </label>
                                    </div>
                                    <div className="form-check form-check-inline">
                                        <input
                                            type="radio"
                                            className="form-check-input"
                                            id="not-sync"
                                            value={0}
                                            {...register('is_sync')}
                                        />
                                        <label className="form-check-label" htmlFor="not-sync">
                                            Không đồng bộ
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </>
                    )}
                    {rootArticle && (
                        <div className="mb-1">
                            <label className="form-label">Bài gốc</label>
                            <Link
                                to={`/article/edit/${articlePageType}/${articleTypeId}/${rootArticle.id}`}
                                className="text-primary"
                            >
                                {rootArticle.title}
                            </Link>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};
