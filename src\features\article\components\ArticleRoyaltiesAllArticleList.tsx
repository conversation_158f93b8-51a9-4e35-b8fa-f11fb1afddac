import React from 'react';
import { useTranslation } from 'react-i18next';
import { ArticleType, ArticleTypeNames } from '../../../types/common/Item';
import { CategoryType } from '../../../types/Category';
import { genTableIndex } from '../../../utils/common';
import { formatDateTime, FORMAT_DATE } from '../../../utils/date';
import { Eye } from 'react-feather';
import Article from '../../../types/Article';
import { Paging } from '../../../types/common';
import ArticleCategory from '../../../types/ArticleCategory';
import { Link } from 'react-router-dom';

interface IProps {
    articles: Article[];
    paging: Paging;
}

export default function ArticleRoyaltiesAllArticleList({ articles, paging }: Readonly<IProps>) {
    const { t } = useTranslation();

    return (
        <tbody>
            {articles.map((article, index) => {
                const articleTypeName = ArticleTypeNames.find((type) => type.id === article.article_type_id);
                const hasChildren = article.childrenArticles && article.childrenArticles.length > 0;

                if (hasChildren) {
                    return (
                        <React.Fragment key={article.id}>
                            <tr>
                                <td>{genTableIndex(index, paging.limit, paging.current_page)}</td>
                                <td>
                                    <div className="d-flex align-items-center">
                                        <div className="avatar-wrapper">
                                            <div className="avatar avatar-sm me-1">
                                                <img
                                                    src={article.image || '/assets/images/portrait/small/avatar-s-11.jpg'}
                                                    alt="Avatar"
                                                    width="32"
                                                    height="32"
                                                />
                                            </div>
                                        </div>
                                        <div className="d-flex flex-column">
                                            <span className="emp_name text-truncate font-weight-bold">
                                                {article.title}
                                            </span>
                                            <small className="emp_post text-truncate text-muted">
                                                {formatDateTime(article.created_at, FORMAT_DATE.DD_MM_YYYY_HH_MM)}
                                            </small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span className="badge badge-light-primary">
                                        {articleTypeName ? t(`${articleTypeName.name}.single`) : 'N/A'}
                                    </span>
                                </td>
                                <td>
                                    {article.articleCategories
                                        ?.filter((category) => category.category.type === CategoryType.ARTICLE_CATEGORY)
                                        .map((category: ArticleCategory) => (
                                            <span key={category.id} className="badge badge-light-info me-1">
                                                {category.category.name}
                                            </span>
                                        ))}
                                </td>
                                <td>
                                    <span className="badge badge-light-secondary">
                                        {article.createdByUser?.name || 'N/A'}
                                    </span>
                                </td>
                                <td className="text-center">
                                    <span
                                        className={`badge ${
                                            article.workflow?.workflow_type_id === 1
                                                ? 'badge-light-danger'
                                                : article.workflow?.workflow_type_id === 2
                                                ? 'badge-light-info'
                                                : 'badge-light-success'
                                        }`}
                                    >
                                        {article.workflow?.name || 'N/A'}
                                    </span>
                                </td>
                                <td className="text-center">
                                    <div className="d-flex justify-content-center">
                                        <Link
                                            to="/"
                                            className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                            title="Xem"
                                        >
                                            <Eye size={14} />
                                        </Link>
                                    </div>
                                </td>
                            </tr>
                            {/* Các row hiển thị thông tin của childrenArticles */}
                            {article.childrenArticles.map((childArticle) => (
                                <tr key={childArticle.id}>
                                    <td></td>
                                    <td>
                                        <div className="d-flex align-items-center">
                                            <div className="avatar-wrapper">
                                                <div className="avatar avatar-sm me-1">
                                                    <img
                                                        src={
                                                            childArticle.image ||
                                                            '/assets/images/portrait/small/avatar-s-11.jpg'
                                                        }
                                                        alt="Avatar"
                                                        width="32"
                                                        height="32"
                                                    />
                                                </div>
                                            </div>
                                            <div className="d-flex flex-column">
                                                <span className="emp_name text-truncate font-weight-bold">
                                                    {childArticle.title}
                                                </span>
                                                <small className="emp_post text-truncate text-muted">
                                                    {formatDateTime(
                                                        childArticle.created_at,
                                                        FORMAT_DATE.DD_MM_YYYY_HH_MM
                                                    )}
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span className="badge badge-light-primary">
                                            {articleTypeName ? t(`${articleTypeName.name}.single`) : 'N/A'}
                                        </span>
                                    </td>
                                    <td>
                                        {childArticle.articleCategories
                                            ?.filter(
                                                (category) => category.category.type === CategoryType.ARTICLE_CATEGORY
                                            )
                                            .map((category: ArticleCategory) => (
                                                <span key={category.id} className="badge badge-light-info me-1">
                                                    {category.category.name}
                                                </span>
                                            ))}
                                    </td>
                                    <td>
                                        <span className="badge badge-light-secondary">
                                            {childArticle.createdByUser?.name || 'N/A'}
                                        </span>
                                    </td>
                                    <td className="text-center">
                                        <span
                                            className={`badge ${
                                                childArticle.workflow?.workflow_type_id === 1
                                                    ? 'badge-light-danger'
                                                    : childArticle.workflow?.workflow_type_id === 2
                                                    ? 'badge-light-info'
                                                    : 'badge-light-success'
                                            }`}
                                        >
                                            {childArticle.workflow?.name || 'N/A'}
                                        </span>
                                    </td>
                                    <td className="text-center">
                                        <div className="d-flex justify-content-center">
                                            <Link
                                                to="/"
                                                className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                                title="Xem"
                                            >
                                                <Eye size={14} />
                                            </Link>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </React.Fragment>
                    );
                } else {
                    return (
                        <tr key={article.id}>
                            <td>{genTableIndex(index, paging)}</td>
                            <td>
                                <div className="d-flex align-items-center">
                                    <div className="avatar-wrapper">
                                        <div className="avatar avatar-sm me-1">
                                            <img
                                                src={article.image || '/assets/images/portrait/small/avatar-s-11.jpg'}
                                                alt="Avatar"
                                                width="32"
                                                height="32"
                                            />
                                        </div>
                                    </div>
                                    <div className="d-flex flex-column">
                                        <span className="emp_name text-truncate font-weight-bold">{article.title}</span>
                                        <small className="emp_post text-truncate text-muted">
                                            {formatDateTime(article.created_at, FORMAT_DATE.DD_MM_YYYY_HH_MM)}
                                        </small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span className="badge badge-light-primary">
                                    {articleTypeName ? t(`${articleTypeName.name}.single`) : 'N/A'}
                                </span>
                            </td>
                            <td>
                                {article.articleCategories
                                    ?.filter((category) => category.category.type === CategoryType.ARTICLE_CATEGORY)
                                    .map((category: ArticleCategory) => (
                                        <span key={category.id} className="badge badge-light-info me-1">
                                            {category.category.name}
                                        </span>
                                    ))}
                            </td>
                            <td>
                                <span className="badge badge-light-secondary">
                                    {article.createdByUser?.name || 'N/A'}
                                </span>
                            </td>
                            <td className="text-center">
                                <span
                                    className={`badge ${
                                        article.workflow?.workflow_type_id === 1
                                            ? 'badge-light-danger'
                                            : article.workflow?.workflow_type_id === 2
                                            ? 'badge-light-info'
                                            : 'badge-light-success'
                                    }`}
                                >
                                    {article.workflow?.name || 'N/A'}
                                </span>
                            </td>
                            <td className="text-center">
                                <div className="d-flex justify-content-center">
                                    <Link
                                        to="/"
                                        className="btn btn-icon btn-sm btn-flat-primary waves-effect"
                                        title="Xem"
                                    >
                                        <Eye size={14} />
                                    </Link>
                                </div>
                            </td>
                        </tr>
                    );
                }
            })}
        </tbody>
    );
}
