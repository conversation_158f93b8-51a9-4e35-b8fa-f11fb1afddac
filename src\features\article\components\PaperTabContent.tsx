import FlightTakeoffIcon from '@mui/icons-material/FlightTakeoff';
import HistoryIcon from '@mui/icons-material/History';
import RotateRightIcon from '@mui/icons-material/RotateRight';
import classNames from 'classnames';
import ListIssuePageFile from 'features/issue/components/ListIssuePageFile';
import React, { useMemo } from 'react';
import { Book, Edit, Edit3, Eye, Image, Plus, Printer, Trash } from 'react-feather';
import { Link, useSearchParams } from 'react-router-dom';
import Issue from 'types/Issue';
import IssuePageFile from 'types/IssuePageFile';
import cn from 'utils/cn';
import { ARTICLE_TAB, LIMIT_MAX } from '../../../constants/common';
import { PlanTypes, PlanTypesName, TypeSettingStatus, TypeSettingStatusNames } from '../../../types/Article';
import ArticleCategory from '../../../types/ArticleCategory';
import { CategoryType } from '../../../types/Category';
import IssuePage from '../../../types/IssuePage';
import User from '../../../types/User';
import { WorkflowType } from '../../../types/Workflow';
import { getArticlePageType } from '../../../utils/common';

interface IProps {
    activeTab: number;
    onOpenModal: (id: number) => void;
    issuePages: IssuePage[];
    setArticleTypeSettingStatusTo: React.Dispatch<React.SetStateAction<number | null>>;
    setArticleSelectedId: React.Dispatch<React.SetStateAction<number | null>>;
    setShowModalChangeStatus: React.Dispatch<React.SetStateAction<boolean>>;
    currentUser: User | null;
    handleMoveIssuePage: (issuePageId: number, articleIssuePageId: number) => void;
    onCloneArticle: (articleId: number) => void;
    onDeleteArticleIssuePage: (articleIssuePageId: number) => void;
    issues: Issue[];
    issuePageFiles: IssuePageFile[];
    handleEdit: (id: number) => void;
    handleDelete: (id: number) => void;
    handleAdd: () => void;
}

export default function PaperTabContent({
    activeTab,
    onOpenModal,
    issuePages,
    setArticleTypeSettingStatusTo,
    setArticleSelectedId,
    setShowModalChangeStatus,
    currentUser,
    handleMoveIssuePage,
    onCloneArticle,
    onDeleteArticleIssuePage,
    issues,
    issuePageFiles,
    handleEdit,
    handleDelete,
    handleAdd,
}: Readonly<IProps>) {
    const [searchParams] = useSearchParams();

    const isShowAddIssuePageFile = useMemo(
        () => searchParams.get('press_publication_id') && searchParams.get('issue_id'),
        [searchParams]
    );

    const renderTabContent = () => {
        if (activeTab === PlanTypes.APPROVE) {
            return (
                <div className="w-100">
                    {isShowAddIssuePageFile && (
                        <div className="d-flex justify-content-end mb-2">
                            <button
                                className="d-flex gap-25 align-items-center btn-icon btn btn-primary btn-round btn-sm waves-effect waves-float waves-light"
                                type="button"
                                onClick={handleAdd}
                            >
                                <Plus size={16} />
                                <span className="text-sm">Thêm duyệt trang báo</span>
                            </button>
                        </div>
                    )}
                    <ListIssuePageFile
                        items={issuePageFiles}
                        issues={issues}
                        paging={{
                            count_item: 1,
                            total_page: 1,
                            current_page: 1,
                            limit: LIMIT_MAX,
                        }}
                        handleEdit={handleEdit}
                        handleDelete={handleDelete}
                        issuePages={issuePages}
                    />
                </div>
            );
        }

        return issuePages.map((page, index) => {
            let articleFiltered = page.articleIssuePages;
            if (activeTab === PlanTypes.WAITING) {
                articleFiltered = articleFiltered?.filter(
                    (articleIssuePage) => articleIssuePage.article.typesetting_status_id !== TypeSettingStatus.WAIT
                );
            } else {
                articleFiltered = articleFiltered?.filter(
                    (articleIssuePage) => articleIssuePage.article.typesetting_status_id === TypeSettingStatus.WAIT
                );
            }

            if (activeTab === PlanTypes.ORGANIZATION || activeTab === PlanTypes.WAITING) {
                articleFiltered = articleFiltered?.filter(
                    (articleIssuePage) => articleIssuePage.article.workflow.workflow_type_id === WorkflowType.PUBLISHING
                );
            }

            return (
                <div
                    className="max-w-4xl bg-white border border-gray-300 rounded-lg h-[1000px] max-h-[1000px] overflow-auto w-[685px]"
                    key={`page-${index + 1}`}
                >
                    <div className="flex justify-between items-center p-1 border-b border-gray-300 bg-gray-50">
                        <h3 className="text-lg font-semibold">{page.name}</h3>
                        <Link
                            to={`edit/${page.id}`}
                            className="flex items-center bg-[#a42c48] bg-opacity-90 text-white rounded hover:bg-opacity-100"
                            style={{ padding: '5px' }}
                        >
                            <Eye size={13} className="mr-1" />
                            Xem trang
                        </Link>
                    </div>

                    <div className="px-1 py-2">
                        {articleFiltered?.map((articleIssuePage) => (
                            <div className="border border-gray-200 rounded-lg p-1 mb-2" key={articleIssuePage.id}>
                                <div className="flex gap-4">
                                    <div className="w-48 h-48 bg-gray-100 border border-1 border-gray-300 rounded flex items-center justify-center flex-shrink-0">
                                        {articleIssuePage.article.avatar1?.file_url ? (
                                            <img
                                                src={articleIssuePage.article.avatar1.file_url}
                                                alt="Article avatar"
                                                className="w-full h-full object-cover rounded"
                                            />
                                        ) : (
                                            <Image size={32} className="text-gray-400" />
                                        )}
                                    </div>

                                    <div className="flex-1">
                                        <div className="text-gray-600 mb-1">{articleIssuePage.article.title}</div>
                                        <div className="text-gray-500 space-y-1">
                                            <div>
                                                <strong>Trang thái bài viết:</strong>{' '}
                                                {articleIssuePage.article.workflow?.name || 'Chưa có trạng thái'}
                                            </div>
                                            <div>
                                                <strong>Trang thái đăng trang:</strong>{' '}
                                                {TypeSettingStatusNames.find(
                                                    (status) =>
                                                        status.id === articleIssuePage.article.typesetting_status_id
                                                )?.name ?? 'Chưa có trạng thái'}
                                            </div>
                                            <div>
                                                <strong>Chuyên mục:</strong>
                                                <span>
                                                    {articleIssuePage.article.articleCategories &&
                                                    articleIssuePage.article.articleCategories.length > 0
                                                        ? articleIssuePage.article.articleCategories
                                                              .filter(
                                                                  (ac: ArticleCategory) =>
                                                                      ac.category?.category_type_id ===
                                                                      CategoryType.CATEGORY
                                                              )
                                                              .map((ac: ArticleCategory) => (
                                                                  <div key={ac.id}>{ac.category?.name}</div>
                                                              ))
                                                        : 'Chưa có chuyên mục'}
                                                </span>
                                            </div>
                                            <div>
                                                <strong>Tác giả:</strong>{' '}
                                                {articleIssuePage.article.pseudonym?.name || 'Chưa có tác giả'}
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex flex-col gap-2">
                                        <div className="btn-group">
                                            <button
                                                className="text-gray-400 hover:text-gray-600 btn btn-primary dropdown-toggle waves-effect waves-light"
                                                type="button"
                                                id="pagePlanDropdownAction"
                                                data-bs-toggle="dropdown"
                                                data-bs-auto-close="true"
                                                aria-expanded="false"
                                            >
                                                <Edit size={16} />
                                            </button>
                                            <ul className="dropdown-menu" aria-labelledby="pagePlanDropdownAction">
                                                <li>
                                                    <Link
                                                        className="dropdown-item waves-effect !w-full"
                                                        to={`/article/edit/${getArticlePageType(
                                                            articleIssuePage.article,
                                                            currentUser!
                                                        )}/${articleIssuePage.article.article_type_id}/${
                                                            articleIssuePage.article.id
                                                        }`}
                                                        target="_blank"
                                                    >
                                                        <Edit3 size={14} className="me-1" />
                                                        <span>Chỉnh sửa bài viết</span>
                                                    </Link>
                                                </li>
                                                {(activeTab === PlanTypes.PAPER ||
                                                    activeTab === PlanTypes.ORGANIZATION) && (
                                                    <>
                                                        <li>
                                                            <button
                                                                type="button"
                                                                className="dropdown-item waves-effect !w-full"
                                                                disabled={
                                                                    articleIssuePage.article.workflow
                                                                        .workflow_type_id !== WorkflowType.PUBLISHING
                                                                }
                                                                onClick={() => {
                                                                    setArticleTypeSettingStatusTo(
                                                                        TypeSettingStatus.DOING
                                                                    );
                                                                    setArticleSelectedId(articleIssuePage.article.id!);
                                                                    setShowModalChangeStatus(true);
                                                                }}
                                                            >
                                                                <Printer size={14} className="me-1" />
                                                                <span>Chuyển in</span>
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button
                                                                type="button"
                                                                className="dropdown-item waves-effect !w-full"
                                                                onClick={() =>
                                                                    handleMoveIssuePage(page.id!, articleIssuePage.id!)
                                                                }
                                                            >
                                                                <Book size={14} className="me-1" />
                                                                <span>Chuyển trang</span>
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button
                                                                type="button"
                                                                className="dropdown-item waves-effect !w-full"
                                                                onClick={() =>
                                                                    onCloneArticle(articleIssuePage.article.id!)
                                                                }
                                                            >
                                                                <FlightTakeoffIcon fontSize="small" className="me-1" />
                                                                <span>Chuyển website</span>
                                                            </button>
                                                        </li>
                                                    </>
                                                )}
                                                {activeTab === PlanTypes.WAITING && (
                                                    <>
                                                        <li>
                                                            <button
                                                                type="button"
                                                                className="dropdown-item waves-effect !w-full"
                                                                onClick={() => {
                                                                    setArticleTypeSettingStatusTo(
                                                                        TypeSettingStatus.WAIT
                                                                    );
                                                                    setArticleSelectedId(articleIssuePage.article.id!);
                                                                    setShowModalChangeStatus(true);
                                                                }}
                                                            >
                                                                <RotateRightIcon fontSize="small" className="me-1" />
                                                                <span>Kéo về</span>
                                                            </button>
                                                        </li>
                                                        <li>
                                                            <button
                                                                type="button"
                                                                className="dropdown-item waves-effect !w-full"
                                                                onClick={() => {
                                                                    setArticleTypeSettingStatusTo(
                                                                        TypeSettingStatus.DONE
                                                                    );
                                                                    setArticleSelectedId(articleIssuePage.article.id!);
                                                                    setShowModalChangeStatus(true);
                                                                }}
                                                            >
                                                                <Printer size={14} className="me-1" />
                                                                <span>Hoàn thành</span>
                                                            </button>
                                                        </li>
                                                    </>
                                                )}
                                                <li>
                                                    <Link
                                                        className="dropdown-item waves-effect !w-full"
                                                        to={`/article/edit/${getArticlePageType(
                                                            articleIssuePage.article,
                                                            currentUser!
                                                        )}/${articleIssuePage.article.article_type_id}/${
                                                            articleIssuePage.article.id
                                                        }?tab=${ARTICLE_TAB.HISTORY}`}
                                                        target="_blank"
                                                    >
                                                        <HistoryIcon fontSize="small" className="me-1" />
                                                        <span>Lịch sử</span>
                                                    </Link>
                                                    {articleIssuePage.article.typesetting_status_id ===
                                                        TypeSettingStatus.WAIT && (
                                                        <button
                                                            type="button"
                                                            className="dropdown-item waves-effect !w-full"
                                                            onClick={() =>
                                                                onDeleteArticleIssuePage(articleIssuePage.id!)
                                                            }
                                                        >
                                                            <Trash size={14} className="me-1" />
                                                            <span>Xóa</span>
                                                        </button>
                                                    )}
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ))}
                        <button
                            className="flex items-center justify-center gap-2 text-[#a42c48] px-4 py-2 border-2 border-dashed border-gray-300 rounded-lg w-full"
                            onClick={() => onOpenModal(page.id!)}
                        >
                            <Plus size={16} />
                            Thêm bài viết vào trang
                        </button>
                    </div>
                </div>
            );
        });
    };

    return (
        <div className="tab-content mb-1">
            {PlanTypesName.map((item) => (
                <div
                    className={classNames('tab-pane fade', { 'show active': activeTab === item.id })}
                    id={`navs-top-${item.id}`}
                    role="tabpanel"
                    key={item.id}
                >
                    <div className="overflow-x-auto">
                        <div
                            className={cn('flex flex-row w-max gap-2', {
                                'w-100': activeTab === PlanTypes.APPROVE,
                            })}
                        >
                            {renderTabContent()}
                        </div>
                    </div>
                </div>
            ))}
        </div>
    );
}
