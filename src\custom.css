/**
 * Custom styles for form elements
 */

/* Input group text styles for password toggle */
.input-group-merge .input-group-text {
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Error state - Ưu tiên cao nhất */
.form-control.error + .input-group-text,
.form-control.error ~ .input-group-text {
  border-color: #ea5455 !important;
}

/* Focus state - Chỉ áp dụng khi không có lỗi */
.form-control:not(.error):focus + .input-group-text,
.form-control:not(.error):focus ~ .input-group-text {
  border-color: #a42c48ff !important;
}

/* Hover state - Chỉ áp dụng khi không có lỗi và không focus */
.input-group-merge:hover .form-control:not(.error):not(:focus) ~ .input-group-text,
.input-group-merge:hover .form-control:not(.error):not(:focus) + .input-group-text {
  border-color: #b8b8b8;
}

/* Disabled state */
.form-control:disabled + .input-group-text,
.form-control[readonly] + .input-group-text {
  background-color: #efefef;
  border-color: #d8d6de;
}

/* Dark theme support */
.dark-layout .input-group-text {
  background-color: #283046;
  border-color: #404656;
}

.dark-layout .form-control:not(.error):focus + .input-group-text,
.dark-layout .form-control:not(.error):focus ~ .input-group-text {
  border-color: #a42c48ff !important;
}

.dark-layout .form-control.error + .input-group-text,
.dark-layout .form-control.error ~ .input-group-text {
  border-color: #ea5455 !important;
}

/* Đảm bảo error state luôn được ưu tiên cao nhất */
.form-control.error:focus + .input-group-text,
.form-control.error:focus ~ .input-group-text {
  border-color: #ea5455 !important;
}

.dropdown-toggle.dropdown-toggle-no-arrow::after {
  display: none;
}

/* Apply for DateRangePicker */
.new-date-range-picker .rs-input-group {
  border: 0;
}
.new-date-range-picker .rs-input-group.rs-input-group-inside .rs-input {
  color: #6e6b7b;
  padding: 0;
}
.new-date-range-picker .rs-input-group.rs-input-group-inside .rs-input:focus {
  border: 0;
  outline: none;
}
.new-date-range-picker .rs-input-group.rs-input-group-inside .rs-input-group-addon {
  padding: 0;
}
.new-date-range-picker .rs-input-group:not(.rs-input-group-disabled).rs-input-group-focus, .rs-input-group:focus-within {
  outline: none;
}
.vertical-layout.vertical-menu-modern.menu-expanded .main-menu {
  width: 270px;
}