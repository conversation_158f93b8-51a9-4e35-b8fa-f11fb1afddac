import classNames from 'classnames';
import { isEmpty } from 'lodash';
import { X } from 'react-feather';
import { FileData } from 'types/UploadFile';

type Props = {
    show: boolean;
    changeShow: (s: boolean) => void;
    files: FileData[];
    fullName?: string;
};

const ModalViewUserFiles = ({ show, changeShow, files, fullName }: Props) => {
    return (
        <>
            <div
                className={classNames('modal fade text-start modal-primary z-[1350]', { show })}
                style={{ display: show ? 'block' : 'none' }}
            >
                <div className="modal-dialog modal-dialog-centered modal-lg">
                    <div className="modal-content">
                        <div className="flex px-[20px] pt-6 mb-2">
                            <h5 className="text-2xl font-bold text-[#171A1FFF] flex-1">Media của {fullName}</h5>
                            <button type="button" onClick={() => changeShow(false)}>
                                <X />
                            </button>
                        </div>

                        <div className="px-6 pb-6">
                            <div className="grid grid-cols-3 gap-2 content-start mb-1">
                                {isEmpty(files) ? (
                                    <div className="text-center text-gray-500 py-8">Không có ảnh</div>
                                ) : (
                                    <>
                                        {files.map((item, index) => (
                                            <div key={index} className="relative group">
                                                {/* Image Container */}
                                                <div className="relative">
                                                    <img
                                                        src={item.file_url || ''}
                                                        alt={item.file_name || ''}
                                                        className={
                                                            'w-full h-auto object-cover border-gray-200 border-2 rounded-lg'
                                                        }
                                                        style={{
                                                            aspectRatio: '16/9',
                                                        }}
                                                    />
                                                </div>

                                                {/* Image Title */}
                                                <div className="mt-1">
                                                    <h3 className="text-lg font-medium text-gray-900 truncate">
                                                        {item.file_title || 'Untitled'}
                                                    </h3>
                                                    <div className="flex flex-wrap gap-1 mt-1">
                                                        {/* Display actual tags from API */}
                                                        {item.tags?.map((tag, tagIndex) => (
                                                            <span
                                                                key={tagIndex}
                                                                className="inline-block badge rounded-pill badge-light-danger me-50 mb-50"
                                                            >
                                                                {tag.name}
                                                            </span>
                                                        ))}
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {show && <div className="modal-backdrop fade show" />}
        </>
    );
};

export default ModalViewUserFiles;
